<?xml version="1.0" encoding="UTF-8"?>

<?import de.jensd.fx.glyphs.fontawesome.FontAwesomeIconView?>
<?import java.lang.String?>
<?import javafx.collections.FXCollections?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.chart.BarChart?>
<?import javafx.scene.chart.CategoryAxis?>
<?import javafx.scene.chart.NumberAxis?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressBar?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.VBox?>

<VBox alignment="CENTER" maxHeight="Infinity" maxWidth="Infinity" minHeight="-Infinity" minWidth="-Infinity" spacing="20.0" styleClass="root" stylesheets="@../../../Styles/Statistics.css" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.store.app.petstore.Controllers.Admin.Statistic.OverViewController">
    <fx:include source="../AdminMenu.fxml" />
    <HBox prefHeight="118.0" prefWidth="1286.0" spacing="20" styleClass="hbox-statistic" HBox.hgrow="ALWAYS">
        <padding>
            <Insets top="20" />
        </padding>
        <HBox maxWidth="500" minWidth="250" styleClass="statistic-item-tag" HBox.hgrow="ALWAYS">
            <Pane prefHeight="50.0" prefWidth="50.0">
                <FontAwesomeIconView glyphName="DOLLAR" layoutX="8.0" layoutY="38.0" size="40" styleClass="item-tag-icon" />
            </Pane>
            <VBox maxWidth="400" prefHeight="62.0" prefWidth="191.0" spacing="5">
                <Label styleClass="item-tag-title" text="Tổng doanh thu trong ngày:" />
                <HBox alignment="CENTER_LEFT" spacing="5">
                    <Label fx:id="totalRevenueLabel" prefHeight="35.0" prefWidth="152.0" styleClass="item-tag-number" text="0.00" />
                    <Label styleClass="item-tag-unit" text="VND" />
                </HBox>
            </VBox>
        </HBox>
        <HBox maxWidth="Infinity" minWidth="200" styleClass="statistic-item-tag" HBox.hgrow="ALWAYS">
            <Pane prefHeight="35.0" prefWidth="35.0">
                <FontAwesomeIconView glyphName="PAW" layoutX="-4.0" layoutY="28.0" size="35" styleClass="item-tag-icon" />
            </Pane>
            <VBox maxWidth="Infinity" prefHeight="50.0">
                <Label styleClass="item-tag-title" text="Tổng thú cưng đã bán trong ngày:" />
                <HBox prefHeight="100.0" prefWidth="200.0">
                    <children>
                        <Label fx:id="totalPetsSoldLabel" prefHeight="17.0" prefWidth="141.0" text="0.00">
                            <styleClass>
                                <String fx:value="item-tag-text" />
                                <String fx:value="item-tag-number" />
                            </styleClass>
                        </Label>
                        <Label styleClass="item-tag-unit" text="con" />
                    </children>
                </HBox>
                <VBox alignment="CENTER_RIGHT" prefHeight="200.0" prefWidth="100.0" />
            </VBox>
        </HBox>
        <HBox maxWidth="Infinity" minWidth="200" styleClass="statistic-item-tag" HBox.hgrow="ALWAYS">
            <Pane prefHeight="35.0" prefWidth="35.0">
                <FontAwesomeIconView glyphName="ARCHIVE" layoutX="-4.0" layoutY="29.0" size="35" styleClass="item-tag-icon" />
            </Pane>
            <VBox maxWidth="Infinity" prefHeight="50.0">
                <Label styleClass="item-tag-title" text="Tổng sản phẩm đã bán trong ngày:" />
                <HBox prefHeight="100.0" prefWidth="200.0">
                    <Label fx:id="totalProductsSoldLabel" prefHeight="17.0" prefWidth="141.0" text="0">
                        <styleClass>
                            <String fx:value="item-tag-text" />
                            <String fx:value="item-tag-number" />
                        </styleClass>
                    </Label>
                    <Label prefHeight="17.0" prefWidth="89.0" styleClass="item-tag-unit" text="sản phẩm" />
                </HBox>
                <VBox alignment="CENTER_RIGHT" prefHeight="200.0" prefWidth="100.0" />
            </VBox>
        </HBox>
        <HBox maxWidth="400" minWidth="200" styleClass="statistic-item-tag" HBox.hgrow="ALWAYS">
            <Pane prefHeight="35.0" prefWidth="35.0">
                <FontAwesomeIconView glyphName="CALCULATOR" layoutX="-4.0" layoutY="29.0" size="35" styleClass="item-tag-icon" />
            </Pane>
            <VBox maxWidth="Infinity" prefHeight="50.0">
                <Label styleClass="item-tag-title" text="Tổng hóa đơn trong ngày:" />
                <HBox prefHeight="100.0" prefWidth="200.0">
                    <Label fx:id="totalInvoicesLabel" prefHeight="17.0" prefWidth="121.0" text="0">
                        <styleClass>
                            <String fx:value="item-tag-text" />
                            <String fx:value="item-tag-number" />
                        </styleClass>
                    </Label>
                    <Label prefHeight="17.0" prefWidth="53.0" styleClass="item-tag-unit" text="hóa đơn" />
                </HBox>
                <VBox alignment="CENTER_RIGHT" prefHeight="200.0" prefWidth="100.0" />
            </VBox>
        </HBox>
    </HBox>
    <HBox spacing="10.0" HBox.hgrow="ALWAYS" VBox.vgrow="ALWAYS">
        <padding>
            <Insets top="20" />
        </padding>
        <VBox maxWidth="Infinity" minWidth="300" styleClass="main-statistic-tag" HBox.hgrow="ALWAYS" VBox.vgrow="ALWAYS">
            <HBox prefHeight="50" spacing="10.0">
                <Label prefHeight="29.0" prefWidth="310.0" styleClass="main-statistic-text" text="Biểu đồ thống kê doanh thu" />
                <DatePicker fx:id="startDatePicker" prefHeight="30.0" prefWidth="110.0">
                    <styleClass>
                        <String fx:value="choice-box-filter" />
                        <String fx:value="yellow" />
                    </styleClass>
                </DatePicker>
                <DatePicker fx:id="endDatePicker" prefHeight="30.0" prefWidth="110.0">
                    <styleClass>
                        <String fx:value="choice-box-filter" />
                        <String fx:value="yellow" />
                    </styleClass>
                </DatePicker>
                <Button fx:id="viewRevenueButton" onAction="#onViewRevenueButtonClicked" prefHeight="30.0" styleClass="view-button" text="Xem" />
            </HBox>
            <ProgressBar fx:id="chartProgressBar" layoutX="10.0" layoutY="60.0" prefWidth="200.0" visible="false" />
            <BarChart fx:id="summaryChart" animated="false" legendVisible="false" maxHeight="Infinity" maxWidth="Infinity" minHeight="400">
                <xAxis>
                    <CategoryAxis label="Tháng" />
                </xAxis>
                <yAxis>
                    <NumberAxis label="Doanh thu (triệu VND)" />
                </yAxis>
            </BarChart>
        </VBox>
        <VBox maxWidth="Infinity" minWidth="200" styleClass="main-statistic-tag" HBox.hgrow="ALWAYS" VBox.vgrow="ALWAYS">
            <HBox maxWidth="Infinity" spacing="10">
                <padding>
                    <Insets bottom="20" right="10" />
                </padding>
                <Label prefHeight="21.0" prefWidth="270.0" styleClass="main-statistic-text" text="Tổng hóa đơn gần đây" />
                <ChoiceBox prefHeight="30.0" prefWidth="160">
                    <styleClass>
                        <String fx:value="choice-box-filter" />
                        <String fx:value="yellow" />
                    </styleClass>
                    <items>
                        <FXCollections fx:factory="observableArrayList">
                            <String fx:value="Tháng 1" />
                            <String fx:value="Tháng 2" />
                            <String fx:value="Tháng 3" />
                            <String fx:value="Tháng 4" />
                            <String fx:value="Tháng 5" />
                            <String fx:value="Tháng 6" />
                            <String fx:value="Tháng 7" />
                            <String fx:value="Tháng 8" />
                            <String fx:value="Tháng 9" />
                            <String fx:value="Tháng 10" />
                            <String fx:value="Tháng 11" />
                            <String fx:value="Tháng 12" />
                        </FXCollections>
                    </items>
                </ChoiceBox>
                <Button fx:id="viewRecentOrdersButton" onAction="#onViewRecentOrdersButtonClicked" prefHeight="30.0" styleClass="view-button" text="Xem" />
            </HBox>
            <ProgressBar fx:id="tableProgressBar" layoutX="10.0" layoutY="60.0" prefWidth="200.0" visible="false" />
            <TableView fx:id="recentOrdersTable" minHeight="400" minWidth="600" styleClass="table-view" HBox.hgrow="ALWAYS">
                <columnResizePolicy>
                    <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                </columnResizePolicy>
                <columns>
                    <TableColumn fx:id="colBillID" prefWidth="100" text="Mã hóa đơn" />
                    <TableColumn fx:id="colNameCustomer" prefWidth="150" text="Khách hàng" />
                    <TableColumn fx:id="colTime" prefWidth="100" text="Thời gian" />
                    <TableColumn fx:id="colTotalMoney" prefWidth="100" text="Tổng tiền" />
                </columns>
            </TableView>
        </VBox>
    </HBox>
</VBox>
