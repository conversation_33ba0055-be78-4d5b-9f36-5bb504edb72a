<?xml version="1.0" encoding="UTF-8"?>

<?import java.lang.String?>
<?import javafx.collections.FXCollections?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.chart.BarChart?>
<?import javafx.scene.chart.CategoryAxis?>
<?import javafx.scene.chart.NumberAxis?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressBar?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>

<AnchorPane styleClass="root" stylesheets="@../../../Styles/Statistics.css" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.store.app.petstore.Controllers.Admin.Statistic.RevenueController">

    <VBox alignment="CENTER" spacing="20.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <fx:include source="../AdminMenu.fxml" />
        <HBox spacing="20.0" style="-fx-padding: 20px" AnchorPane.bottomAnchor="20" AnchorPane.topAnchor="180" VBox.vgrow="ALWAYS">
            <VBox styleClass="main-statistic-tag" HBox.hgrow="ALWAYS" VBox.vgrow="ALWAYS">
                <HBox alignment="CENTER_LEFT" prefHeight="50" spacing="10.0">
                    <VBox spacing="10">
                        <Label styleClass="main-statistic-text" text="Biểu đồ thống kê doanh thu theo nhân viên" />
                        <HBox alignment="CENTER_LEFT" spacing="20" HBox.hgrow="ALWAYS">
                            <DatePicker fx:id="revenueChartFilter1" prefHeight="30.0" prefWidth="110.0">
                                <styleClass>
                                    <String fx:value="date-picker-filter" />
                                    <String fx:value="yellow" />
                                </styleClass>
                            </DatePicker>
                            <Label text="đến" />
                            <DatePicker fx:id="revenueChartFilter2" prefHeight="30.0" prefWidth="110.0">
                                <styleClass>
                                    <String fx:value="date-picker-filter" />
                                    <String fx:value="yellow" />
                                </styleClass>
                            </DatePicker>
                            <Button fx:id="viewChartButton" onAction="#handleViewChartButtonAction" prefHeight="30.0" styleClass="view-button" text="Xem" />
                        </HBox>
                    </VBox>
                </HBox>
                <ProgressBar fx:id="chartProgressBar" prefWidth="200" visible="false" VBox.vgrow="NEVER" />
                <BarChart fx:id="summaryChart" animated="false" legendVisible="false" prefHeight="400" VBox.vgrow="ALWAYS">
                    <xAxis>
                        <CategoryAxis label="Nhân viên" />
                    </xAxis>
                    <yAxis>
                        <NumberAxis label="Doanh thu (triệu VND)" />
                    </yAxis>
                </BarChart>
            </VBox>
            <VBox styleClass="main-statistic-tag" HBox.hgrow="ALWAYS" VBox.vgrow="ALWAYS">
                <HBox alignment="CENTER_LEFT" prefHeight="20.0" prefWidth="422.0" spacing="10">
                    <padding>
                        <Insets bottom="20" />
                    </padding>
                    <VBox spacing="10">
                        <Label styleClass="main-statistic-text" text="Doanh thu theo nhân viên" />
                        <HBox spacing="20" />

                    </VBox>
                      <ChoiceBox fx:id="staffRevenueFilter" prefHeight="30.0" prefWidth="160" HBox.hgrow="ALWAYS">
                          <styleClass>
                              <String fx:value="choice-box-filter" />
                              <String fx:value="yellow" />
                          </styleClass>
                          <items>
                              <FXCollections fx:factory="observableArrayList">
                                  <String fx:value="Tháng 1" />
                                  <String fx:value="Tháng 2" />
                                  <String fx:value="Tháng 3" />
                                  <String fx:value="Tháng 4" />
                                  <String fx:value="Tháng 5" />
                                  <String fx:value="Tháng 6" />
                                  <String fx:value="Tháng 7" />
                                  <String fx:value="Tháng 8" />
                                  <String fx:value="Tháng 9" />
                                  <String fx:value="Tháng 10" />
                                  <String fx:value="Tháng 11" />
                                  <String fx:value="Tháng 12" />
                              </FXCollections>
                          </items>
                      </ChoiceBox>
                      <Button fx:id="viewStaffButton" onAction="#handleViewStaffButtonAction" prefHeight="30.0" styleClass="view-button" text="Xem" />
                </HBox>
                <ProgressBar fx:id="staffTableProgressBar" prefWidth="200" visible="false" VBox.vgrow="NEVER" />
                <TableView fx:id="staffRevenueTable" prefHeight="400" styleClass="table-view" VBox.vgrow="ALWAYS">
                    <columnResizePolicy>
                        <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                    </columnResizePolicy>
                    <columns>
                        <TableColumn fx:id="colNameEmpl" prefWidth="150" text="Tên nhân viên">
                            <cellValueFactory>
                                <PropertyValueFactory property="staffName" />
                            </cellValueFactory>
                        </TableColumn>
                  <TableColumn fx:id="colTotalBIll" prefWidth="150" text="Số hóa đơn">
                     <cellValueFactory>
                        <PropertyValueFactory property="staffName" />
                     </cellValueFactory>
                  </TableColumn>
                        <TableColumn fx:id="colTotalMoney" prefWidth="100" text="Doanh thu">
                            <cellValueFactory>
                                <PropertyValueFactory property="totalRevenue" />
                            </cellValueFactory>
                        </TableColumn>
                    </columns>
                </TableView>
            </VBox>
        </HBox>
    </VBox>
</AnchorPane>
